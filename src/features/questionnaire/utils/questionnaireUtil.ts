import type { DynamicFormData } from '@/features/project-management/utils/initialScreeningUtils';
import { QuestionType } from '../types/questionnaire';
import type { OptionsExtend, QuestionInSectionExtend, SectionsQuestionnaire } from '../types/questionnaire';
import { z } from 'zod';

const handleRemoveSpecialSymbol = (text: string) => {
  const cleanedString = text
    .normalize('NFD')
    .replace(/[\u0300-\u036F]/g, '')
    .replace(/[^a-z0-9\s]/gi, '');

  const words = cleanedString.trim().split(/\s+/);
  const fieldName = words
    .map((word, index) =>
      index === 0
        ? word.toLowerCase()
        : word.charAt(0).toUpperCase() + word.slice(1).toLowerCase(),
    )
    .join('');

  return fieldName;
};

export function generateDefaultValuesForm(
  questions: QuestionInSectionExtend[],
): DynamicFormData {
  const defaultValues: DynamicFormData = {};

  questions.forEach((question) => {
    defaultValues[question.name] = question.answer || '';
  });

  return defaultValues;
}

export function createDynamicSchema(questions: QuestionInSectionExtend[]): z.ZodObject<any> {
  const schemaFields: Record<string, z.ZodString> = {};

  questions.forEach((question) => {
    schemaFields[question.name] = z.string();
  });

  return z.object(schemaFields).catchall(z.string().optional());
}

export const handleAddFieldNameInData = (section: SectionsQuestionnaire) => {
  const addNameField = (data: any, field: string, index?: number) => {
    return data[field].map((d: any, idx: number) => ({
      ...d,
      name: `${d.isCustom ? 'other_' : ''}${handleRemoveSpecialSymbol(d.title)}_${d.type ? d.type : data.type
      }_${idx}${index ? `_${index}` : ''}`,
      ...(d?.options && { options: addNameField(d, 'options', idx) }),
      ...(d?.subQuestions && { subQuestions: addNameField(d, 'subQuestions', idx) }),
    }));
  };

  return (addNameField(section, 'questions'));
};

export function updateOptionValue(question: QuestionInSectionExtend, options: OptionsExtend[], data: any) {
  return options.map(option => ({
    ...option,
    isSelected: option.isCustom ? !!data[option.name] : (data[question.name] === option.title || data[question.name]?.includes(option.title)),
    ...(option.isCustom && { answer: data[option.name] }),
  }));
}

export function updateSubQuestionValue(question: QuestionInSectionExtend, subQuestions: QuestionInSectionExtend[], data: any) {
  return subQuestions.map(subQuestion => ({
    ...subQuestion,
    ...(question.type === QuestionType.CATEGORY && {
      options: updateOptionValue(question, subQuestion.options, data),
    }),

    ...(question.type === QuestionType.MULTIPLE_TEXT && {
      answer: data[subQuestion.name],
    }),
  }));
}
