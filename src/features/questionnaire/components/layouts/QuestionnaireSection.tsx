'use client';

import React, { useImperativeHandle, useRef } from 'react';
import QuestionnaireForm from './QuestionnaireForm';
import type { QuestionnaireFormTypeRef } from './QuestionnaireForm';

export type QuestionnaireSectionTypeRef = {
  onSubmitForm: () => void;
};

type QuestionnaireSectionType = {
  section: any;
  ref?: React.Ref<QuestionnaireSectionTypeRef>;
};

const QuestionnaireSection: React.FC<QuestionnaireSectionType> = ({ section, ref }) => {
  const form = useRef<QuestionnaireFormTypeRef>(null);

  useImperativeHandle(ref, () => {
    return {
      onSubmitForm: () => {
        const data = form.current?.onSubmitForm();
        return data;
      },
    };
  }, []);

  return (
    <div>

      <div className=" p-1.5 px-4 border-l-4 border-sky-600">
        <h4 className="text-sky-600">{section.title}</h4>
        <div
          onClick={() => form?.current?.onSubmitForm()}
          className=""
        >
          {section.description}
        </div>
      </div>

      <QuestionnaireForm ref={form} questions={section.questions} />
    </div>
  );
};

export default QuestionnaireSection;
