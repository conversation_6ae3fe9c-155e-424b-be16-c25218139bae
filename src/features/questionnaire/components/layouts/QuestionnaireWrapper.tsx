'use client';

import React, { useEffect, useRef } from 'react';
import { DocumentIcon } from '@/shared/icons';
import QuestionnaireSection from './QuestionnaireSection';
import type { QuestionnaireSectionTypeRef } from './QuestionnaireSection';
import ProjectCardSkeleton from '@/features/project-management/components/project-list/ProjectCardSkeleton';
import { useQuestionnaireData, useQuestionnaireStore } from '../../stores';
import QuestionnaireMarkdown from './QuestionnaireMarkdown';
import { MOCK_QUESTIONNAIRE } from '../../constants/mock-questionnaire';
import type { QuestionInSectionExtend, QuestionnairePayload, QuestionnaireResponse } from '../../types/questionnaire';
import { Button } from '@/shared/components/ui/button';
import { useCreateQuestionnaire } from '../../hooks/useQuestionnaireCreate';

const QuestionnaireWrapper: React.FC = () => {
  const questionnaire = useQuestionnaireData();

  const { mutateAsync } = useCreateQuestionnaire();

  const formRef = useRef<QuestionnaireSectionTypeRef[]>([]);

  const questionnaireStore = useQuestionnaireStore()!.getState();

  const { actions } = questionnaireStore;

  const { initializeQuestionnaire } = actions;

  useEffect(() => {
    initializeQuestionnaire(MOCK_QUESTIONNAIRE as QuestionnaireResponse);
  }, [initializeQuestionnaire]);

  const handleSubmit = () => {
    const payload = ((questionnaire?.sections ?? []).map((section, index) => ({
      ...section,
      questions: (formRef.current[index]?.onSubmitForm() ?? []) as QuestionInSectionExtend[],
    }))) as QuestionnairePayload[];

    console.log(payload);
    // FIXME: update later
    if (payload && payload.length && payload[0]) {
      mutateAsync(payload[0]);
    }
  };

  return (
    <div className="min-h-screen bg-[#EEF2FF] py-8 px-4">
      <div className="max-w-5xl mx-auto">
        {
          !questionnaire
            ? (
                <>
                  <h3>Waiting...</h3>
                  <ProjectCardSkeleton />
                </>
              )
            : (
                <>
                  <div className="text-center mb-8">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                      <DocumentIcon className="w-8 h-8 text-blue-600" />
                    </div>
                    <h1 className="text-2xl font-semibold text-gray-900 mb-2">
                      Market Sizing Questionnaire
                    </h1>
                    <p className="text-gray-600 mb-1">
                      Help us understand your vision needs and preferences
                    </p>
                    <p className="text-sm text-blue-600">
                      *This survey takes approximately 10-15 minutes to complete.
                    </p>
                  </div>

                  {questionnaire.introduction && (
                    <div className="bg-white shadow-sm rounded-2xl p-4 mb-4">
                      <QuestionnaireMarkdown markdown={questionnaire.introduction} />
                    </div>
                  )}

                  {questionnaire.sections.map((section, index) => (
                    <div key={index} className="bg-white shadow-sm rounded-2xl p-4">
                      <QuestionnaireSection
                        ref={(e) => {
                          if (e) {
                            formRef.current[index] = e;
                          }
                        }}
                        section={section}
                      />
                    </div>
                  ))}

                </>
              )
        }
        <div className="sticky bottom-0 bg-white shadow-md p-2 flex justify-center items-center border-t border-t-gray-200 rounded-xl mt-2">
          <Button onClick={() => handleSubmit()}>Submit</Button>
        </div>
      </div>
    </div>
  );
};

export default QuestionnaireWrapper;
