'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { submitQuestionnaire } from '../services/questionnaire.service';
import type { QuestionnairePayload } from '../types/questionnaire';

export function useCreateQuestionnaire() {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: QuestionnairePayload) => submitQuestionnaire(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['createQuestionnaire'] });
    },
    onError: (error) => {
      console.error('Error creating questionnaire:', error);
    },
  });
}
