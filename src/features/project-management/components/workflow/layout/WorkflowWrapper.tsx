'use client';

import type React from 'react';
import { useEffect } from 'react';
import { useProjectSteps } from '@/features/project-management/hooks';
import WorkflowSkeleton from './WorkflowSkeleton';
import {
  useCurrentStep,
  useCurrentTask,
  useWorkflowStore,
  // useInitializeWorkflow,

} from '@/features/project-management/stores/project-workflow-store';
import { cn } from '@/shared/utils/utils';
import { ChatBox } from '../chat-with-ai/ChatBox';
import TaskList from './TaskList';
import TaskSidebar from './TaskSidebar';
import { useChatBoxVisible } from '@/features/project-management/stores/chatbox-store';
import EvaluationForm from '../evaluation-form/EvaluationForm';
import InitialScreeningForm from '../initial-screening-form/InitialScreeningForm';
import ScreeningOutcomeWrapper from '../screening-outcome/ScreeningOutcomeWrapper';
import { ETypeStep } from '@/features/project-management/types/workflow';
import type { WorkflowStep } from '@/features/project-management/types/workflow';
import ClientUploadForm from '../client-upload-form/ClientUploadForm';
import BriefAnalysisWrapper from '../brief-analysis/BriefAnalysisWrapper';
import GenerationFormWrapper from '../generation-form/GenerationFormWrapper';
import { useCoAgent } from '@copilotkit/react-core';
import type { stateRouteAgent } from '@/shared/types/global';
import { AGENT_NAME_COPILOTKIT, AGENT_ROUTE_NAME } from '@/shared/constants/global';
import { useParams } from 'next/navigation';
import BriefStandardizedWrapper from '../brief-analysis/BriefStandardizedWrapper';
import ReviewInputWrapper from '../review-input/ReviewInputWrapper';
import DocumentUploadFile from '../questionnaire/DocumentUploadFile';
import GeneratedQuestionnaire from '../questionnaire/GeneratedQuestionnaire';

const WorkflowWrapper: React.FC<{ projectId: string }> = ({ projectId }) => {
  const { data: projectSteps, isLoading } = useProjectSteps(projectId);

  const currentTask = useCurrentTask();
  const currentStep = useCurrentStep();

  const workflowStore = useWorkflowStore()!.getState();
  const { actions } = workflowStore;
  const { initializeWorkflow } = actions;

  const params = useParams<{ id: string }>();

  const { state: _agentState, setState } = useCoAgent<stateRouteAgent<any>>({
    name: AGENT_ROUTE_NAME,
  });

  useEffect(() => {
    setState(prev => ({
      ...prev,
      project_id: params.id,
      agent_name: prev?.agent_name || AGENT_NAME_COPILOTKIT.SUMMARIZE, // Ensure agent_name is set
    }));
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params.id]);

  // const { initializeWorkflow } = useWorkflowActions();

  // Initialize workflow when projectSteps data is available
  useEffect(() => {
    // const workflowTasks = transformProjectStepsToHierarchy(projectSteps ?? []);
    const workflowTasks: WorkflowStep[] = (projectSteps ?? []).map(p => ({ ...p, steps: p.children ?? [] }));

    initializeWorkflow(workflowTasks);
  }, [projectSteps, initializeWorkflow]); // initializeWorkflow is stable in Zustand but included for ESLint

  const isVisible = useChatBoxVisible();

  // Render the appropriate step component based on the current step
  const renderStepComponent = () => {
    if (!currentStep) {
      return null;
    }

    // Render based on step ID and type
    if (currentStep.type === ETypeStep.FORM) {
      return <InitialScreeningForm />;
    } else if (currentStep.type === ETypeStep.EVALUATION) {
      return <EvaluationForm />;
    } else if (currentStep.type === ETypeStep.OUTCOME) {
      return <ScreeningOutcomeWrapper />;
    } else if (currentStep.type === ETypeStep.UPLOAD) {
      return <ClientUploadForm />;
    } else if (currentStep.type === ETypeStep.GENERATE) {
      return <BriefAnalysisWrapper />;
    } else if (currentStep.type === ETypeStep.STANDARDIZED) {
      return <BriefStandardizedWrapper />;
    } else if (currentStep.type === ETypeStep.REVIEW_1 || currentStep.type === ETypeStep.REVIEW_2) {
      return <ReviewInputWrapper type={currentStep.type} />;
    } else if (currentStep.type === ETypeStep.GENERATED || currentStep.type === ETypeStep.GENERATED2) {
      return <GenerationFormWrapper type={currentStep.type} />;
    } else if (currentStep.type === ETypeStep.DOCUMENT) {
      return <DocumentUploadFile />;
    } else if (currentStep.type === ETypeStep.QUESTIONNAIRE) {
      return <GeneratedQuestionnaire />;
    } else {
      return null;
    }
  };

  return (isLoading || !currentStep || !currentTask)
    ? (
        <WorkflowSkeleton />
      )
    : (
        <div className="grid grid-cols-12 border-t border-border h-[calc(100vh-64px)] overflow-hidden">
          {/* Right Sidebar with task list */}
          <div className={cn(
            'col-span-12 md:col-span-2 h-full overflow-y-auto border-r border-border',
          )}
          >
            <TaskList />
          </div>

          {/* Main content area */}
          <div className={cn(
            'col-span-12 md:col-span-10 h-full relative',
            isVisible ? 'lg:col-span-7' : 'lg:col-span-10',
          )}
          >
            <div className="absolute inset-0 overflow-y-auto">
              <div className="min-h-full flex flex-col">
                <div className="flex-grow relative">
                  { currentStep
                    ? (
                        <>
                          <h6 className="p-4 md:p-6 pb-0!">
                            {`${currentTask.order + 1}.${currentStep.order + 1}. ${currentStep.name}`}
                          </h6>

                          {/* Render the appropriate step component */}
                          {renderStepComponent()}
                        </>
                      )
                    : (
                        <div className="p-4 md:p-6">Select a task to begin</div>
                      )}
                </div>
              </div>
            </div>
          </div>

          {/* Left sidebar with AI chat */}
          {isVisible && (
            <div className="hidden lg:block lg:col-span-3 h-full border-l border-border">
              <div className="h-full overflow-y-auto">
                <TaskSidebar>
                  <ChatBox />
                </TaskSidebar>
              </div>
            </div>
          )}
        </div>
      );
};

export default WorkflowWrapper;
