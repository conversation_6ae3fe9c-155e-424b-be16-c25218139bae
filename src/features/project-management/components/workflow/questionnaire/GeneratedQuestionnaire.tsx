import { useState } from 'react';
import ProjectCardSkeleton from '../../project-list/ProjectCardSkeleton';

const GeneratedQuestionnaire: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);

  return (
    isLoading
      ? (
          <div className="p-4 md:p-6 ">
            <div className="mb-1 md:mb-2">Analyzing</div>
            <ProjectCardSkeleton />
          </div>
        )
      : (
          <div className="p-4 md:p-6 ">
           <div className='flex p-1 rounded-md'> 
              
           </div>
          </div>
        )
  );
};

export default GeneratedQuestionnaire;
